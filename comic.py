import os
import base64
import streamlit as st
from google import genai
from google.genai.types import Part
from google.genai import types
from openai import OpenAI
from PIL import Image
from io import BytesIO
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get API keys from environment variables
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# --- Step 1: Extract comic prompt from video using Gemini Flash ---
def extract_comic_prompt_and_enhance(video_url, user_prompt):
    client = genai.Client(api_key=GEMINI_API_KEY)
    
    # Send the video URL and the user-provided prompt to <PERSON> for analysis and enhancement
    response = client.models.generate_content(
        model="gemini-2.0-flash",
        contents=[
            Part(text=f'''Convert this video into a 4-panel comic strip. For each panel:
            Describe the image (characters, expressions, motion, setting, layout)
            Provide dialogue in speech bubbles and caption text.
            Make it humorous, exaggerating the timing and reactions like a classic meme.
            Comic style only – no photorealism. If the user has mentioned any movie name, just remove it from the user prompt but keep the character name and try to make the comic look like that character.{user_prompt}'''),
            Part(file_data={"file_uri": video_url, "mime_type": "video/mp4"})
        ]
    )
    
    # Return the enhanced prompt from Gemini's response
    return response.text

# --- Step 2: Enrich the prompt with comic-style constraints ---
def enrich_prompt(raw_prompt):
    return f"""Generate a 4-panel comic strip image laid out clearly in a 2x2 grid. 
Each panel must be clearly framed and equally spaced.
Ensure that no part of the speech bubbles, dialogue, or characters is cropped or cut off.
Text should be fully visible and readable inside speech bubbles or captions.
Use a colorful comic style with bold outlines, meme-like expressions, and exaggerated reactions.
Avoid photorealism completely.

{raw_prompt}"""

# --- Step 3: Primary image generation using OpenAI GPT-4 Vision ---
def generate_with_openai(prompt, filename):
    print("🎨 Attempting image generation with OpenAI GPT-4 Vision...")
    client = OpenAI(api_key=OPENAI_API_KEY)
    try:
        result = client.images.generate(
            model="gpt-image-1",
            prompt=prompt,
            size="1792x1024",  # Optimized size for comic strips
            quality="standard",  # Use standard quality for faster generation
            response_format="b64_json"
        )
        image_base64 = result.data[0].b64_json
        image_bytes = base64.b64decode(image_base64)

        # Save to file for consistency
        with open(filename, "wb") as f:
            f.write(image_bytes)
        print("✅ OpenAI image generated successfully:", filename)
        return image_bytes
    except Exception as e:
        print(f"❌ OpenAI generation failed: {e}")
        return None

# --- Step 4: Fallback image generation with Imagen-4 ---
def generate_with_gemini_imagen(prompt, filename):
    print("🔄 Fallback: Attempting image generation with Google Imagen-4...")
    client = genai.Client(api_key=GEMINI_API_KEY)
    try:
        response = client.models.generate_images(
            model='imagen-4',  # Updated to use imagen-4
            prompt=prompt,
            config=types.GenerateImagesConfig(
                number_of_images=1,
                aspect_ratio="16:9",
                output_mime_type="image/png"
            )
        )

        # Check if the response contains the generated images
        if not response.generated_images:
            print("⚠️ No image generated by Google Imagen.")
            return None

        # Get the first generated image (it's already a PIL Image object)
        image_data = response.generated_images[0].image

        # Save the PIL Image directly to a file on disk
        image_data.save(filename)
        print("✅ Gemini image generated successfully:", filename)

        # Convert PIL image to bytes for consistency with OpenAI function
        img_byte_arr = BytesIO()
        image_data.save(img_byte_arr, format='PNG')
        return img_byte_arr.getvalue()

    except Exception as e:
        print(f"❌ Gemini generation failed: {e}")
        return None


# --- Optimized Streamlit App ---
def main():
    st.set_page_config(page_title="Comic War", layout="wide")

    # Initialize session state for better performance
    if 'comic_generated' not in st.session_state:
        st.session_state.comic_generated = False
    if 'comic_image' not in st.session_state:
        st.session_state.comic_image = None
    if 'comic_source' not in st.session_state:
        st.session_state.comic_source = None

    # Custom CSS for better appearance
    st.markdown("""
    <style>
    .main-header {
        font-size: 2.5rem;
        color: #FF4B4B;
        text-align: center;
        margin-bottom: 2rem;
    }
    .subheader {
        font-size: 1.5rem;
        color: #4B4BFF;
        margin-top: 1rem;
    }
    .stButton>button {
        background-color: #FF4B4B;
        color: white;
        font-weight: bold;
        padding: 0.5rem 2rem;
        border-radius: 5px;
    }
    .success-message {
        color: #28a745;
        font-weight: bold;
    }
    </style>
    """, unsafe_allow_html=True)

    st.markdown("<h1 class='main-header'>Comic War</h1>", unsafe_allow_html=True)

    col1, col2 = st.columns([1, 1])

    with col1:
        st.markdown("<h3 class='subheader'>Input</h3>", unsafe_allow_html=True)
        # Video URL input (no video player)
        youtube_url = st.text_input("Enter YouTube Shorts URL:",
                                    placeholder="https://youtube.com/shorts/...")

        # Prompt for comic description
        user_prompt = st.text_area("Enter your comic description:",
                                  placeholder="Describe the style, characters, or theme you want...",
                                  height=150)

        if st.button("Generate Comic", type="primary"):
            if youtube_url and user_prompt:
                # Clear previous results
                st.session_state.comic_generated = False
                st.session_state.comic_image = None
                st.session_state.comic_source = None

                # Create progress indicators
                progress_bar = st.progress(0)
                status_text = st.empty()

                try:
                    # Step 1: Extract and enhance prompt
                    status_text.text("🔍 Analyzing video and enhancing prompt...")
                    progress_bar.progress(25)
                    enhanced_prompt = extract_comic_prompt_and_enhance(youtube_url, user_prompt)
                    enriched_prompt = enrich_prompt(enhanced_prompt)

                    # Step 2: Generate image with OpenAI first (primary)
                    status_text.text("🎨 Generating comic with GPT-4 Vision...")
                    progress_bar.progress(50)
                    image_bytes = generate_with_openai(enriched_prompt, "comic_openai.png")

                    if image_bytes:
                        st.session_state.comic_image = image_bytes
                        st.session_state.comic_source = "OpenAI GPT-4 Vision"
                        progress_bar.progress(100)
                        status_text.markdown("<span class='success-message'>✅ Comic generated successfully with OpenAI!</span>", unsafe_allow_html=True)
                    else:
                        # Fallback to Gemini Imagen-4
                        status_text.text("🔄 Trying fallback with Google Imagen-4...")
                        progress_bar.progress(75)
                        image_bytes = generate_with_gemini_imagen(enriched_prompt, "comic_gemini.png")

                        if image_bytes:
                            st.session_state.comic_image = image_bytes
                            st.session_state.comic_source = "Google Imagen-4"
                            progress_bar.progress(100)
                            status_text.markdown("<span class='success-message'>✅ Comic generated successfully with Imagen-4!</span>", unsafe_allow_html=True)
                        else:
                            progress_bar.progress(100)
                            status_text.error("❌ Both image generation services failed. Please try again.")

                    if st.session_state.comic_image:
                        st.session_state.comic_generated = True

                except Exception as e:
                    progress_bar.progress(100)
                    status_text.error(f"❌ Error during generation: {str(e)}")

            else:
                st.warning("Please enter both YouTube URL and comic description.")

    # Display generated comic in the second column
    with col2:
        st.markdown("<h3 class='subheader'>Generated Comic</h3>", unsafe_allow_html=True)

        if st.session_state.comic_generated and st.session_state.comic_image:
            # Display the generated comic
            img = Image.open(BytesIO(st.session_state.comic_image))
            st.image(img, caption=f"Comic generated by {st.session_state.comic_source}", use_column_width=True)

            # Download button
            st.download_button(
                label="📥 Download Comic",
                data=st.session_state.comic_image,
                file_name=f"comic_{st.session_state.comic_source.lower().replace(' ', '_').replace('-', '_')}.png",
                mime="image/png",
                type="secondary"
            )
        else:
            st.info("Your comic will appear here after generation.")

if __name__ == "__main__":
    main()
