import os
import base64
import streamlit as st
from google import genai
from google.genai.types import Part
from google.genai import types
from openai import OpenAI
from PIL import Image
from io import BytesIO
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get API keys from environment variables
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# --- Step 1: Extract comic prompt from video using Gemini Flash ---
def extract_comic_prompt_and_enhance(video_url, user_prompt):
    client = genai.Client(api_key=GEMINI_API_KEY)
    
    # Send the video URL and the user-provided prompt to <PERSON> for analysis and enhancement
    response = client.models.generate_content(
        model="gemini-2.0-flash",
        contents=[
            Part(text=f'''Convert this video into a 4-panel comic strip. For each panel:
            Describe the image (characters, expressions, motion, setting, layout)
            Provide dialogue in speech bubbles and caption text.
            Make it humorous, exaggerating the timing and reactions like a classic meme.
            Comic style only – no photorealism. If the user has mentioned any movie name, just remove it from the user prompt but keep the character name and try to make the comic look like that character.{user_prompt}'''),
            Part(file_data={"file_uri": video_url, "mime_type": "video/mp4"})
        ]
    )
    
    # Return the enhanced prompt from Gemini's response
    return response.text

# --- Step 2: Enrich the prompt with comic-style constraints ---
def enrich_prompt(raw_prompt):
    return f"""Generate a 4-panel comic strip image laid out clearly in a 2x2 grid. 
Each panel must be clearly framed and equally spaced.
Ensure that no part of the speech bubbles, dialogue, or characters is cropped or cut off.
Text should be fully visible and readable inside speech bubbles or captions.
Use a colorful comic style with bold outlines, meme-like expressions, and exaggerated reactions.
Avoid photorealism completely.

{raw_prompt}"""

# --- Step 3: Generate image using OpenAI DALL·E ---
def generate_with_openai(prompt, filename):
    print("🎨 Attempting image generation with OpenAI...")
    client = OpenAI(api_key=OPENAI_API_KEY)
    try:
        result = client.images.generate(
            model="gpt-image-1",
            prompt=prompt,
            # size="1536x1024"
        )
        image_base64 = result.data[0].b64_json
        image_bytes = base64.b64decode(image_base64)
        with open(filename, "wb") as f:
            f.write(image_bytes)
        print("OpenAI image saved as:", filename)
        return image_bytes
    except Exception as e:
        print(f"❌ OpenAI generation failed: {e}")
        return None

# --- Step 4: Fallback image generation with Gemini ---
def generate_with_gemini_imagen(prompt, filename):
    print("Fallback: Attempting image generation with Google Imagen...")
    client = genai.Client(api_key=GEMINI_API_KEY)
    response = client.models.generate_images(
        model='imagen-4.0-generate-preview-06-06',
        prompt=prompt,
        config=types.GenerateImagesConfig(
            number_of_images=1,
            aspect_ratio="16:9"
        )
    )
    
    # Check if the response contains the generated images
    if not response.generated_images:
        print("⚠️ No image generated by Google Imagen.")
        return None
    
    # Get the first generated image (it's already a PIL Image object)
    image_data = response.generated_images[0].image
    
    # Save the PIL Image directly to a file on disk
    image_data.save(filename)
    print("Gemini image saved as:", filename)

    return filename  # Return the file path in case you need to reference it later


# --- Streamlit App ---
def main():
    st.set_page_config(page_title="Comic War", layout="wide")
    
    # Custom CSS for better appearance
    st.markdown("""
    <style>
    .main-header {
        font-size: 2.5rem;
        color: #FF4B4B;
        text-align: center;
        margin-bottom: 2rem;
    }
    .subheader {
        font-size: 1.5rem;
        color: #4B4BFF;
        margin-top: 1rem;
    }
    .stButton>button {
        background-color: #FF4B4B;
        color: white;
        font-weight: bold;
        padding: 0.5rem 2rem;
        border-radius: 5px;
    }
    </style>
    """, unsafe_allow_html=True)
    
    st.markdown("<h1 class='main-header'>Comic War</h1>", unsafe_allow_html=True)
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown("<h3 class='subheader'>Input</h3>", unsafe_allow_html=True)
        # Video URL input (no video player)
        youtube_url = st.text_input("Enter YouTube Shorts URL:", 
                                    placeholder="https://youtube.com/shorts/...")
        
        # Prompt for comic description
        user_prompt = st.text_area("Enter your comic description:", 
                                  placeholder="Describe the style, characters, or theme you want...",
                                  height=150)
        
        if st.button("Generate Comic"):
            if youtube_url and user_prompt:
                with st.spinner("Generating comic... please wait."):
                    # Enhance the user prompt with Gemini
                    enhanced_prompt = extract_comic_prompt_and_enhance(youtube_url, user_prompt)
                    enriched_prompt = enrich_prompt(enhanced_prompt)
                    
                    # Generate image using Gemini first, fallback to OpenAI if Gemini fails
                    image_path_gemini = generate_with_gemini_imagen(enriched_prompt, "comic_gemini.png")
                    image_bytes_openai = None
                    if not image_path_gemini:
                        image_bytes_openai = generate_with_openai(enriched_prompt, "comic_openai.png")
                    
                    # Display generated image in the second column
                    with col2:
                        st.markdown("<h3 class='subheader'>Generated Comic</h3>", unsafe_allow_html=True)
                        if image_bytes_openai:
                            img_openai = Image.open(BytesIO(image_bytes_openai))
                            st.image(img_openai, caption="Comic generated by OpenAI")
                            st.download_button("Download OpenAI Comic", data=image_bytes_openai, 
                                              file_name="comic_openai.png", mime="image/png")
                        elif image_path_gemini:
                            img_gemini = Image.open(image_path_gemini)
                            st.image(img_gemini, caption="Comic generated by Gemini")
                            with open(image_path_gemini, "rb") as file:
                                st.download_button("Download Gemini Comic", data=file, 
                                                  file_name="comic_gemini.png", mime="image/png")
            else:
                st.warning("Please enter both YouTube URL and comic description.")
    
    # Initial empty state for the second column
    with col2:
        if not st.session_state.get('generated', False):
            st.markdown("<h3 class='subheader'>Generated Comic</h3>", unsafe_allow_html=True)
            st.info("Your comic will appear here after generation.")

if __name__ == "__main__":
    main()
